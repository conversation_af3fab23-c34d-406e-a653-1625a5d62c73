package com.facishare.paas.metadata.dataloader.image.extractor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UnifiedImageExtractor 的单元测试
 * 专门测试 hasDISPIMGFunction 方法的优化效果
 */
@ExtendWith(MockitoExtension.class)
class UnifiedImageExtractorTest {

    private UnifiedImageExtractor extractor;

    @BeforeEach
    void setUp() {
        extractor = new UnifiedImageExtractor();
        // 设置为非严格模式进行测试
        ReflectionTestUtils.setField(extractor, "strictModeOnly", false);
    }

    @Test
    void testValidDISPIMGFunctions() {
        // 测试标准的 WPS DISPIMG 函数格式
        assertTrue(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)"));
        
        // 测试 Excel 兼容格式
        assertTrue(callHasDISPIMGFunction("=@_xlfn.DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)"));
        
        // 测试带空格的格式
        assertTrue(callHasDISPIMGFunction("= DISPIMG( \"ID_71D3D639B47E41A295E42E64A2D17E7A\" , 1 )"));
        
        // 测试不同的数字参数
        assertTrue(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",2)"));
        assertTrue(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",10)"));
    }

    @Test
    void testInvalidDISPIMGFunctions() {
        // 测试空值和空字符串
        assertFalse(callHasDISPIMGFunction(null));
        assertFalse(callHasDISPIMGFunction(""));
        assertFalse(callHasDISPIMGFunction("   "));
        
        // 测试不包含 DISPIMG 的字符串
        assertFalse(callHasDISPIMGFunction("=SUM(A1:A10)"));
        assertFalse(callHasDISPIMGFunction("Hello World"));
        
        // 测试 ID 格式错误
        assertFalse(callHasDISPIMGFunction("=DISPIMG(\"INVALID_ID\",1)"));
        assertFalse(callHasDISPIMGFunction("=DISPIMG(\"ID_SHORTID\",1)"));
        assertFalse(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7G\",1)")); // 包含非十六进制字符
        
        // 测试参数格式错误
        assertFalse(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\")"));
        assertFalse(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",abc)"));
    }

    @Test
    void testStrictModeOnly() {
        // 设置为严格模式
        ReflectionTestUtils.setField(extractor, "strictModeOnly", true);
        
        // 标准格式应该通过
        assertTrue(callHasDISPIMGFunction("=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)"));
        
        // 非标准格式在严格模式下应该被拒绝
        // 这里可以添加一些在宽松模式下会通过但在严格模式下会被拒绝的测试用例
    }

    @Test
    void testCaseInsensitive() {
        // 测试大小写不敏感
        assertTrue(callHasDISPIMGFunction("=dispimg(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)"));
        assertTrue(callHasDISPIMGFunction("=DISPIMG(\"id_71d3d639b47e41a295e42e64a2d17e7a\",1)"));
        assertTrue(callHasDISPIMGFunction("=@_XLFN.DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)"));
    }

    @Test
    void testPerformance() {
        // 简单的性能测试
        String validDispimg = "=DISPIMG(\"ID_71D3D639B47E41A295E42E64A2D17E7A\",1)";
        String invalidDispimg = "=SUM(A1:A10)";
        
        long startTime = System.nanoTime();
        
        // 执行多次测试
        for (int i = 0; i < 10000; i++) {
            callHasDISPIMGFunction(validDispimg);
            callHasDISPIMGFunction(invalidDispimg);
        }
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        
        // 验证性能（应该在合理时间内完成）
        assertTrue(duration < 1_000_000_000L, "Performance test should complete within 1 second");
    }

    /**
     * 使用反射调用私有方法 hasDISPIMGFunction
     */
    private boolean callHasDISPIMGFunction(String cellValue) {
        try {
            return (Boolean) ReflectionTestUtils.invokeMethod(extractor, "hasDISPIMGFunction", cellValue);
        } catch (Exception e) {
            fail("Failed to invoke hasDISPIMGFunction: " + e.getMessage());
            return false;
        }
    }
}
