package com.facishare.paas.metadata.dataloader.image.extractor;

import com.facishare.paas.metadata.dataloader.image.model.CellPosition;
import com.facishare.paas.metadata.dataloader.image.model.ImageStorageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 统一图片提取器
 * 支持Office和WPS的所有图片存储格式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class UnifiedImageExtractor {

    // WPS DISPIMG XML命名空间常量
    private static final String WPS_NAMESPACE = "http://www.wps.cn/officeDocument/2017/etCustomData";
    private static final String XDR_NAMESPACE = "http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing";
    private static final String A_NAMESPACE = "http://schemas.openxmlformats.org/drawingml/2006/main";
    private static final String R_NAMESPACE = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";

    // WPS DISPIMG 函数正则表达式常量（预编译以提高性能）
    /**
     * 严格的 DISPIMG 函数匹配模式
     * 格式：=[@_xlfn.]DISPIMG("ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",数字)
     * 示例：=DISPIMG("ID_71D3D639B47E41A295E42E64A2D17E7A",1)
     *      =@_xlfn.DISPIMG("ID_71D3D639B47E41A295E42E64A2D17E7A",1)
     */
    private static final Pattern DISPIMG_STRICT_PATTERN = Pattern.compile(
        "(?i)^=(@?_?xlfn\\.)?DISPIMG\\s*\\(\\s*\"(ID_[0-9A-F]{32})\"\\s*,\\s*(\\d+)\\s*\\)$"
    );

    /**
     * 宽松的 DISPIMG 函数匹配模式（用于向后兼容）
     * 匹配任何包含 DISPIMG 函数调用的字符串
     */
    private static final Pattern DISPIMG_LOOSE_PATTERN = Pattern.compile(
        "(?i).*DISPIMG\\s*\\(.*\\).*"
    );

    private final XMLInputFactory xmlInputFactory = XMLInputFactory.newInstance();

    // 线程安全的WPS图片映射缓存
    private final ConcurrentHashMap<String, Map<String, String>> wpsImageMappingCache = new ConcurrentHashMap<>();


    /**
     * 统一图片提取入口方法（支持多工作表）
     * 支持Office和WPS的所有图片存储格式
     * 合并ZipFile操作，避免重复打开文件
     *
     * @param filePath Excel文件路径
     * @param position 单元格位置
     * @param sheetIndex 工作表索引（0-based）
     * @return 图片字节数据，如果没有图片返回null
     */
    public byte[] extractImage(String filePath, CellPosition position, int sheetIndex, String cellValue) {
        try (ZipFile zipFile = new ZipFile(filePath)) {
            // 在同一个ZipFile实例中完成检测和提取，避免重复文件I/O
            return extractImageInternal(zipFile, position, sheetIndex, cellValue);
        } catch (Exception e) {
            log.error("Error extracting image from {} at position {} sheet {}: {}",
                     filePath, position, sheetIndex, e.getMessage());
            return null;
        }
    }

    /**
     * 🚀 ZipFile连接池优化：使用已打开的ZipFile实例提取图片
     * 性能优化核心：避免重复打开文件，大幅提升性能
     *
     * @param zipFile 已打开的ZipFile实例
     * @param position 单元格位置
     * @param sheetIndex 工作表索引（0-based）
     * @return 图片字节数据，如果没有图片返回null
     */
    public byte[] extractImageWithZipFile(ZipFile zipFile, CellPosition position, int sheetIndex, String cellValue) {
        try {
            // 直接使用传入的ZipFile实例，无需重新打开
            return extractImageInternal(zipFile, position, sheetIndex, cellValue);
        } catch (Exception e) {
            log.error("Error extracting image with cached ZipFile at position {} sheet {}: {}",
                     position, sheetIndex, e.getMessage());
            return null;
        }
    }

    /**
     * 内部图片提取方法，使用已打开的ZipFile实例
     * 性能优化：合并检测和提取逻辑，减少重复操作
     */
    private byte[] extractImageInternal(ZipFile zipFile, CellPosition position, int sheetIndex, String cellValue) {
        // 1. 检测图片存储类型
        ImageStorageType storageType = detectImageStorageTypeInternal(zipFile, position, sheetIndex, cellValue);
        if (storageType == null) {
            log.debug("No image found at position {} in sheet {} of file {}", position, sheetIndex, zipFile.getName());
            return null;
        }
        log.debug("Detected image storage type {} at position {} in sheet {}", storageType, position, sheetIndex);

        // 2. 根据存储类型提取图片
        return extractByStorageTypeInternal(zipFile, position, storageType, sheetIndex, cellValue);
    }



    /**
     * 检测图片存储类型（内部方法，使用已打开的ZipFile）
     * 性能优化：避免重复打开文件
     *
     * @param zipFile 已打开的ZIP文件
     * @param position 单元格位置
     * @param sheetIndex 工作表索引（0-based）
     * @return 图片存储类型，如果没有图片返回null
     */
    private ImageStorageType detectImageStorageTypeInternal(ZipFile zipFile, CellPosition position, int sheetIndex, String cellValue) {
        try {
            // 1. 检查单元格是否包含DISPIMG函数
            if (hasDISPIMGFunction(cellValue)) {
                return ImageStorageType.WPS_DISPIMG;
            }

            // 2. 检查是否有标准drawing.xml定义
            if (hasStandardDrawingDefinition(zipFile, position, sheetIndex)) {
                return ImageStorageType.OFFICE_STANDARD;
            }

            // 3. 检查是否为单元格内嵌图片
            if (hasCellEmbeddedImage(zipFile, position, sheetIndex)) {
                return ImageStorageType.EMBEDDED_CELL;
            }

            return null; // 无图片

        } catch (Exception e) {
            log.warn("Error detecting image storage type at {} in sheet {}: {}", position, sheetIndex, e.getMessage());
            return null;
        }
    }


    /**
     * 根据存储类型提取图片（内部方法，使用已打开的ZipFile）
     *
     * @param zipFile 已打开的ZIP文件
     * @param position 单元格位置
     * @param storageType 存储类型
     * @param sheetIndex 工作表索引（0-based）
     * @return 图片字节数据
     */
    private byte[] extractByStorageTypeInternal(ZipFile zipFile, CellPosition position, ImageStorageType storageType, int sheetIndex, String cellValue) {
        switch (storageType) {
            case OFFICE_STANDARD:
                return extractStandardOOXMLImageInternal(zipFile, position, sheetIndex);

            case WPS_DISPIMG:
                return extractWPSDispimgImageInternal(zipFile, cellValue);

            case WPS_LEGACY:
                log.debug("WPS legacy image extraction not implemented for position {} in sheet {}", position, sheetIndex);
                return null;

            case EMBEDDED_CELL:
                log.debug("Cell embedded image extraction not implemented for position {} in sheet {}", position, sheetIndex);
                return null;

            default:
                log.warn("Unsupported image storage type: {}", storageType);
                return null;
        }
    }

    /**
     * 检查单元格值是否包含有效的 WPS DISPIMG 函数
     *
     * 优化说明：
     * 1. 使用预编译正则表达式提高性能
     * 2. 分层验证：快速检查 -> 精确匹配 -> 宽松匹配（向后兼容）
     * 3. 严格验证 ID 格式（ID_ + 32位十六进制）
     * 4. 更详细的错误日志和异常处理
     *
     * WPS DISPIMG 函数格式：
     * - 标准格式：=DISPIMG("ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",1)
     * - Excel兼容格式：=@_xlfn.DISPIMG("ID_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",1)
     *
     * @param cellValue 单元格的值
     * @return true 如果是有效的 DISPIMG 函数，false 否则
     */
    private boolean hasDISPIMGFunction(String cellValue) {
        // 参数验证
        if (cellValue == null || cellValue.trim().isEmpty()) {
            log.trace("Cell value is null or empty");
            return false;
        }
        String trimmedValue = cellValue.trim();
        try {
            // 第一层：快速检查是否包含 DISPIMG 关键字（性能优化）
            if (!trimmedValue.toUpperCase().contains("DISPIMG")) {
                log.trace("Cell value does not contain DISPIMG keyword: {}", cellValue);
                return false;
            }

            // 第二层：严格的正则匹配（推荐的标准格式）
            Matcher strictMatcher = DISPIMG_STRICT_PATTERN.matcher(trimmedValue);
            if (strictMatcher.matches()) {
                String prefix = strictMatcher.group(1); // @_xlfn. 前缀
                String imageId = strictMatcher.group(2); // ID_xxx
                String parameter = strictMatcher.group(3); // 数字参数

                log.debug("Valid DISPIMG function detected - prefix: '{}', imageId: '{}', parameter: '{}'",
                         prefix != null ? prefix : "none", imageId, parameter);
                return true;
            }

            // 第三层：宽松匹配（用于向后兼容和非标准格式）
            Matcher looseMatcher = DISPIMG_LOOSE_PATTERN.matcher(trimmedValue);
            if (looseMatcher.matches()) {
                return true; // 保持向后兼容
            }

            return false;

        } catch (PatternSyntaxException e) {
            log.error("Regex pattern error in DISPIMG function detection: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("Unexpected error checking DISPIMG function in cell value '{}': {}",
                    cellValue, e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否有标准drawing.xml定义（支持多工作表）
     */
    private boolean hasStandardDrawingDefinition(ZipFile zipFile, CellPosition position, int sheetIndex) {
        try {
            // 动态构建drawing文件路径
            String drawingPath = String.format("xl/drawings/drawing%d.xml", sheetIndex + 1);
            ZipEntry drawingEntry = zipFile.getEntry(drawingPath);

            if (drawingEntry == null) {
                // 尝试默认路径作为fallback
                drawingPath = "xl/drawings/drawing1.xml";
                drawingEntry = zipFile.getEntry(drawingPath);
                if (drawingEntry == null) {
                    log.debug("No drawing file found for sheet index {}", sheetIndex);
                    return false;
                }
            }

            log.debug("Checking drawing file: {}", drawingPath);
            // 检查drawing.xml中是否有该位置的图片定义
            return hasImageAtPositionInDrawing(zipFile, drawingEntry, position);

        } catch (Exception e) {
            log.debug("Error checking standard drawing at {} in sheet {}: {}", position, sheetIndex, e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否为单元格内嵌图片（支持多工作表）
     * 修复说明：基于深度技术分析，Excel中复制粘贴的图片实际上创建的是标准drawing引用，
     * 不是真正的单元格内嵌图片。让这些图片通过hasStandardDrawingDefinition()
     * 被正确分类为OFFICE_STANDARD类型，使用已修复的标准drawing提取逻辑。
     */
    private boolean hasCellEmbeddedImage(ZipFile zipFile, CellPosition position, int sheetIndex) {
        // 基于技术分析：Excel复制粘贴创建的是标准drawing引用，不是真正的单元格内嵌图片
        // 让这些图片走标准drawing流程，使用已修复的路径解析逻辑
        log.debug("Skipping cell embedded image detection for sheet {} - Excel copy-paste creates standard drawings", sheetIndex);
        return false;
    }

    /**
     * 提取标准OOXML图片（内部方法，使用已打开的ZipFile）
     * 性能优化：避免重复打开文件
     */
    private byte[] extractStandardOOXMLImageInternal(ZipFile zipFile, CellPosition position, int sheetIndex) {
        try {
            // 1. 动态查找drawing.xml文件
            String drawingPath = String.format("xl/drawings/drawing%d.xml", sheetIndex + 1);
            ZipEntry drawingEntry = zipFile.getEntry(drawingPath);

            if (drawingEntry == null) {
                // 尝试默认路径作为fallback
                drawingPath = "xl/drawings/drawing1.xml";
                drawingEntry = zipFile.getEntry(drawingPath);
                if (drawingEntry == null) {
                    log.debug("No drawing file found for sheet index {}", sheetIndex);
                    return null;
                }
            }

            log.debug("Extracting from drawing file: {}", drawingPath);

            // 2. 解析drawing.xml，查找图片引用
            String imageRelId = findImageRelationId(zipFile, drawingEntry, position);
            if (imageRelId == null) {
                return null;
            }

            // 3. 解析关系文件，获取图片路径
            String imagePath = resolveImagePath(zipFile, drawingPath, imageRelId);
            if (imagePath == null) {
                return null;
            }

            // 4. 提取图片数据
            return extractImageFromZip(zipFile, imagePath);

        } catch (Exception e) {
            log.error("Error extracting standard OOXML image at {} from sheet {}: {}", position, sheetIndex, e.getMessage());
            return null;
        }
    }

    /**
     * 提取WPS DISPIMG图片（内部方法，使用已打开的ZipFile）
     */
    private byte[] extractWPSDispimgImageInternal(ZipFile zipFile, String cellValue) {
        try {

            // 1. 解析DISPIMG函数，提取图片ID
            String imageId = parseDispimgImageId(cellValue);
            if (imageId == null) {
                return null;
            }

            // 2. 在xl/media/目录中查找对应的图片文件
            String imagePath = findImageByIdInMedia(zipFile, imageId);
            if (imagePath == null) {
                return null;
            }

            // 3. 提取图片数据
            return extractImageFromZip(zipFile, imagePath);

        } catch (Exception e) {
            log.error("Error extracting WPS DISPIMG image at {} from sheet {}: {}", e.getMessage());
            return null;
        }
    }



    /**
     * 从ZIP中提取图片数据 - 性能优化版本
     * 支持：1. xl/media/ 路径  2. WPS DISPIMG函数
     * 性能优化：
     * 1. 预估图片大小，避免ByteArrayOutputStream频繁扩容
     * 2. 增大缓冲区大小，减少系统调用次数
     * 3. 优化内存分配策略
     */
    private byte[] extractImageFromZip(ZipFile zipFile, String imagePath) {
        log.debug("Extracting image from ZIP: {}", imagePath);

        // 参数验证
        if (imagePath == null || imagePath.trim().isEmpty()) {
            log.warn("Invalid image path: {}", imagePath);
            return null;
        }

        String normalizedPath = imagePath.trim().replace('\\', '/');

        try {
            // 尝试简单的路径查找策略
            ZipEntry imageEntry = findImageEntrySimple(zipFile, normalizedPath);

            if (imageEntry == null) {
                log.warn("Image not found: {}", normalizedPath);
                return null;
            }

            // 性能优化：预估图片大小，避免频繁扩容
            long entrySize = imageEntry.getSize();
            int initialCapacity;
            if (entrySize > 0 && entrySize < Integer.MAX_VALUE) {
                initialCapacity = (int) entrySize;
                log.debug("Using exact size for ByteArrayOutputStream: {} bytes", initialCapacity);
            } else {
                // 如果无法获取确切大小，使用经验值（1MB）
                initialCapacity = 1024 * 1024;
                log.debug("Using estimated size for ByteArrayOutputStream: {} bytes", initialCapacity);
            }

            // 提取图片数据
            try (InputStream inputStream = zipFile.getInputStream(imageEntry)) {
                // 性能优化：增大缓冲区，减少系统调用
                byte[] buffer = new byte[64 * 1024]; // 64KB缓冲区（原来8KB）
                ByteArrayOutputStream baos = new ByteArrayOutputStream(initialCapacity);
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }

                byte[] imageData = baos.toByteArray();
                log.debug("Extracted {} bytes from {} (estimated: {})",
                         imageData.length, imageEntry.getName(), initialCapacity);

                return imageData;
            }

        } catch (Exception e) {
            log.error("Error extracting image: {} -> {}", zipFile.getName(), normalizedPath, e);
            return null;
        }
    }

    /**
     * 简化的图片条目查找 - 只支持核心场景
     * 1. xl/media/ 路径
     * 2. 修正 xl/drawings/media/ 到 xl/media/
     */
    private ZipEntry findImageEntrySimple(ZipFile zipFile, String imagePath) {
        // 1. 尝试原始路径
        ZipEntry entry = zipFile.getEntry(imagePath);
        if (entry != null) {
            log.debug("Found with original path: {}", imagePath);
            return entry;
        }

        // 2. 修正 xl/drawings/media/ 到 xl/media/ (核心修复)
        if (imagePath.contains("xl/drawings/media/")) {
            String correctedPath = imagePath.replace("xl/drawings/media/", "xl/media/");
            entry = zipFile.getEntry(correctedPath);
            if (entry != null) {
                log.debug("Found with corrected path: {} -> {}", imagePath, correctedPath);
                return entry;
            }
        }

        // 3. 如果路径不是以xl/media/开头，尝试添加
        if (!imagePath.startsWith("xl/media/")) {
            String fileName = imagePath.substring(imagePath.lastIndexOf('/') + 1);
            String mediaPath = "xl/media/" + fileName;
            entry = zipFile.getEntry(mediaPath);
            if (entry != null) {
                log.debug("Found in xl/media/: {}", mediaPath);
                return entry;
            }
        }

        log.debug("Image not found: {}", imagePath);
        return null;
    }



    /**
     * 获取单元格公式（支持多工作表）
     *
     * @param zipFile ZIP文件
     * @param position 单元格位置
     * @param sheetIndex 工作表索引（0-based）
     * @return 单元格公式，如果没有公式返回null
     */
    private String getCellFormula(ZipFile zipFile, CellPosition position, int sheetIndex) {
        try {
            // 动态构建工作表路径
            String sheetPath = String.format("xl/worksheets/sheet%d.xml", sheetIndex + 1);
            ZipEntry sheetEntry = zipFile.getEntry(sheetPath);

            if (sheetEntry == null) {
                // 尝试默认路径作为fallback
                sheetPath = "xl/worksheets/sheet1.xml";
                sheetEntry = zipFile.getEntry(sheetPath);
                if (sheetEntry == null) {
                    log.debug("No worksheet found for sheet index {}", sheetIndex);
                    return null;
                }
            }

            log.debug("Reading cell formula from sheet: {}", sheetPath);

            try (InputStream is = zipFile.getInputStream(sheetEntry)) {
                XMLStreamReader reader = xmlInputFactory.createXMLStreamReader(is);
                return findCellFormula(reader, position);
            }

        } catch (Exception e) {
            log.error("Error getting cell formula at {} from sheet {}: {}",
                     position, sheetIndex, e.getMessage());
            return null;
        }
    }

    /**
     * 在XML流中查找指定位置的单元格公式
     *
     * @param reader XML流读取器
     * @param position 目标单元格位置
     * @return 单元格公式，如果没有公式返回null
     */
    private String findCellFormula(XMLStreamReader reader, CellPosition position) throws XMLStreamException {
        String targetAddress = position.getExcelAddress();

        while (reader.hasNext()) {
            int event = reader.next();
            if (event == XMLStreamConstants.START_ELEMENT) {
                String elementName = reader.getLocalName();

                if ("c".equals(elementName)) {
                    String cellRef = reader.getAttributeValue(null, "r");
                    if (targetAddress.equals(cellRef)) {
                        // 找到目标单元格，查找公式
                        return extractFormulaFromCell(reader);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 从单元格XML中提取公式
     *
     * @param reader XML流读取器，当前位置在单元格元素
     * @return 公式内容，如果没有公式返回null
     */
    private String extractFormulaFromCell(XMLStreamReader reader) throws XMLStreamException {
        while (reader.hasNext()) {
            int event = reader.next();
            if (event == XMLStreamConstants.START_ELEMENT) {
                String elementName = reader.getLocalName();
                if ("f".equals(elementName)) {
                    return reader.getElementText();
                }
            } else if (event == XMLStreamConstants.END_ELEMENT && "c".equals(reader.getLocalName())) {
                break;
            }
        }
        return null;
    }

    /**
     * 检查drawing.xml中是否有指定位置的图片
     */
    private boolean hasImageAtPositionInDrawing(ZipFile zipFile, ZipEntry drawingEntry, CellPosition position) {
        try (InputStream is = zipFile.getInputStream(drawingEntry)) {
            XMLStreamReader reader = xmlInputFactory.createXMLStreamReader(is);

            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();

                    // 查找锚定元素
                    if ("twoCellAnchor".equals(elementName) || "oneCellAnchor".equals(elementName)) {
                        if (isAnchorAtPosition(reader, position)) {
                            return true;
                        }
                    }
                }
            }

            return false;
        } catch (Exception e) {
            log.debug("Error checking image at position in drawing: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查锚定是否在指定位置（优化版）
     *
     * 重构说明：直接传入目标位置进行匹配，避免重复解析和对象创建
     */
    private boolean isAnchorAtPosition(XMLStreamReader reader, CellPosition position) {
        try {
            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();

                    if ("from".equals(elementName)) {
                        // 直接传入目标位置进行匹配，支持早期退出优化
                        Boolean matches = (Boolean) parseAnchorPositionWithMatch(reader, position);
                        return matches != null && matches;
                    }
                } else if (event == XMLStreamConstants.END_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("twoCellAnchor".equals(elementName) || "oneCellAnchor".equals(elementName)) {
                        break;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            log.debug("Error checking anchor position: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析锚定位置并进行匹配优化
     * 重构说明：
     * 1. 根据OOXML规范正确处理col, colOff, row, rowOff四个元素
     * 2. 支持早期匹配优化：传入目标位置时可提前退出不匹配的情况
     * 3. 统一处理解析和匹配逻辑，提升性能
     *
     * @param reader XML流读取器
     * @param targetPosition 目标位置，如果为null则返回解析的位置
     * @return 如果targetPosition不为null，返回是否匹配；否则返回解析的位置
     */
    private Object parseAnchorPositionWithMatch(XMLStreamReader reader, CellPosition targetPosition) {
        try {
            int col = -1;
            int row = -1;
            long colOff = 0;
            long rowOff = 0;
            boolean earlyExit = false;

            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();

                    if ("col".equals(elementName)) {
                        col = Integer.parseInt(reader.getElementText());
                        // 早期匹配优化：如果列不匹配且有目标位置，标记为早期退出
                        if (targetPosition != null && col != targetPosition.getCol()) {
                            earlyExit = true;
                        }
                    } else if ("row".equals(elementName)) {
                        row = Integer.parseInt(reader.getElementText());
                        // 早期匹配优化：如果行不匹配且有目标位置，标记为早期退出
                        if (targetPosition != null && row != targetPosition.getRow()) {
                            earlyExit = true;
                        }
                    } else if ("colOff".equals(elementName)) {
                        try {
                            colOff = Long.parseLong(reader.getElementText());
                        } catch (NumberFormatException e) {
                            colOff = 0;
                        }
                    } else if ("rowOff".equals(elementName)) {
                        try {
                            rowOff = Long.parseLong(reader.getElementText());
                        } catch (NumberFormatException e) {
                            rowOff = 0;
                        }
                    }
                } else if (event == XMLStreamConstants.END_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("from".equals(elementName) || "to".equals(elementName)) {
                        break;
                    }
                }
            }

            if (col >= 0 && row >= 0) {
                if (targetPosition != null) {
                    // 返回匹配结果
                    boolean matches = !earlyExit && (col == targetPosition.getCol() && row == targetPosition.getRow());
                    log.debug("Position match: target=({}, {}), parsed=({}, {}), colOff={}, rowOff={}, matches={}",
                             targetPosition.getRow(), targetPosition.getCol(), row, col, colOff, rowOff, matches);
                    return matches;
                } else {
                    // 返回解析的位置
                    log.debug("Parsed anchor position: col={}, row={}, colOff={}, rowOff={}", col, row, colOff, rowOff);
                    return CellPosition.of(row, col);
                }
            }

            log.debug("Invalid anchor position: col={}, row={}", col, row);
            return targetPosition != null ? false : null;
        } catch (Exception e) {
            log.debug("Error parsing anchor position: {}", e.getMessage());
            return targetPosition != null ? false : null;
        }
    }



    /**
     * 查找图片关系ID
     */
    private String findImageRelationId(ZipFile zipFile, ZipEntry drawingEntry, CellPosition position) {
        try (InputStream is = zipFile.getInputStream(drawingEntry)) {
            XMLStreamReader reader = xmlInputFactory.createXMLStreamReader(is);

            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();

                    // 查找锚定元素
                    if ("twoCellAnchor".equals(elementName) || "oneCellAnchor".equals(elementName)) {
                        String relId = findRelationIdInAnchor(reader, position);
                        if (relId != null) {
                            return relId;
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("Error finding image relation ID: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 在锚定元素中查找关系ID
     * 重构说明：直接传入目标位置进行匹配，避免重复解析和对象创建
     */
    private String findRelationIdInAnchor(XMLStreamReader reader, CellPosition position) {
        try {
            boolean isTargetPosition = false;
            String relationId = null;

            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("from".equals(elementName)) {
                        // 直接传入目标位置进行匹配，支持早期退出优化
                        Boolean matches = (Boolean) parseAnchorPositionWithMatch(reader, position);
                        isTargetPosition = (matches != null && matches);
                    } else if ("pic".equals(elementName) && isTargetPosition) {
                        relationId = findRelationIdInPicture(reader);
                    }
                } else if (event == XMLStreamConstants.END_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("twoCellAnchor".equals(elementName) || "oneCellAnchor".equals(elementName)) {
                        break;
                    }
                }
            }

            return isTargetPosition ? relationId : null;
        } catch (Exception e) {
            log.debug("Error finding relation ID in anchor: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 在图片元素中查找关系ID
     */
    private String findRelationIdInPicture(XMLStreamReader reader) {
        try {
            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("blip".equals(elementName)) {
                        String embed = reader.getAttributeValue("http://schemas.openxmlformats.org/officeDocument/2006/relationships", "embed");
                        if (StringUtils.isNotBlank(embed)) {
                            return embed;
                        }
                    }
                } else if (event == XMLStreamConstants.END_ELEMENT) {
                    String elementName = reader.getLocalName();
                    if ("pic".equals(elementName)) {
                        break;
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("Error finding relation ID in picture: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析图片路径
     */
    private String resolveImagePath(ZipFile zipFile, String drawingPath, String imageRelId) {
        try {
            // 构建关系文件路径
            String relsPath = drawingPath.replace(".xml", ".xml.rels").replace("xl/drawings/", "xl/drawings/_rels/");
            ZipEntry relsEntry = zipFile.getEntry(relsPath);
            if (relsEntry == null) {
                return null;
            }
            try (InputStream is = zipFile.getInputStream(relsEntry)) {
                XMLStreamReader reader = xmlInputFactory.createXMLStreamReader(is);

                while (reader.hasNext()) {
                    int event = reader.next();
                    if (event == XMLStreamConstants.START_ELEMENT) {
                        String elementName = reader.getLocalName();

                        if ("Relationship".equals(elementName)) {
                            String id = reader.getAttributeValue(null, "Id");
                            String target = reader.getAttributeValue(null, "Target");

                            if (imageRelId.equals(id) && StringUtils.isNotBlank(target)) {
                                // 简化的路径解析：统一指向 xl/media/
                                String fileName = target;

                                // 移除相对路径前缀
                                if (fileName.startsWith("../")) {
                                    fileName = fileName.substring(3);
                                }

                                // 移除media/前缀（如果存在）
                                if (fileName.startsWith("media/")) {
                                    fileName = fileName.substring(6);
                                }

                                // 构建最终路径：统一使用 xl/media/
                                String finalPath = "xl/media/" + fileName;
                                log.debug("Resolved image path: '{}' -> '{}'", target, finalPath);
                                return finalPath;
                            }
                        }
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("Error resolving image path: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析DISPIMG函数中的图片ID
     */
    private String parseDispimgImageId(String dispimgFormula) {
        try {
            // DISPIMG函数格式：=DISPIMG("image_id")
            if (dispimgFormula == null || !dispimgFormula.toUpperCase().contains("DISPIMG")) {
                return null;
            }

            // 提取引号中的图片ID
            int startQuote = dispimgFormula.indexOf('"');
            int endQuote = dispimgFormula.lastIndexOf('"');

            if (startQuote != -1 && endQuote != -1 && startQuote < endQuote) {
                return dispimgFormula.substring(startQuote + 1, endQuote);
            }

            return null;
        } catch (Exception e) {
            log.warn("Error parsing DISPIMG formula: {}", dispimgFormula, e);
            return null;
        }
    }

    /**
     * 在xl/media/目录中查找指定ID的图片文件
     * WPS DISPIMG专用：基于XML映射文件的精确查找
     */
    private String findImageByIdInMedia(ZipFile zipFile, String imageId) {
        try {
            log.debug("Starting WPS DISPIMG image lookup for ID: {}", imageId);

            // WPS映射解析：第一步 - 从cellimages.xml获取rId
            String rId = getWPSImageRId(zipFile, imageId);
            if (rId == null) {
                log.debug("No rId found for DISPIMG ID: {}", imageId);
                return null;
            }

            // WPS映射解析：第二步 - 从cellimages.xml.rels获取图片路径
            String imagePath = getWPSImagePathFromRId(zipFile, rId);
            if (imagePath == null) {
                log.debug("No image path found for rId: {}", rId);
                return null;
            }

            // 构建完整路径
            String fullPath = imagePath.startsWith("xl/") ? imagePath : "xl/" + imagePath;
            log.debug("WPS DISPIMG mapping success: {} -> {} -> {}", imageId, rId, fullPath);

            return fullPath;

        } catch (Exception e) {
            log.error("Error in WPS DISPIMG mapping for ID: {}", imageId, e);
            return null;
        }
    }

    /**
     * 从xl/cellimages.xml获取DISPIMG ID到rId的映射（带缓存优化）
     * 性能优化：使用缓存避免重复解析XML文件
     * WPS格式：<etc:cellImage><xdr:cNvPr name="ID_xxx"/><a:blip r:embed="rId1"/></etc:cellImage>
     */
    private String getWPSImageRId(ZipFile zipFile, String imageId) {
        // 性能优化：检查缓存
        String fileName = zipFile.getName();
        Map<String, String> mapping = getWPSImageMappingCached(zipFile, fileName);

        if (mapping != null) {
            String result = mapping.get(imageId);
            if (result != null) {
                log.debug("WPS DISPIMG mapping from cache: {} -> {}", imageId, result);
            } else {
                log.debug("No matching DISPIMG ID found in cache: {}", imageId);
            }
            return result;
        }

        // 如果缓存构建失败，回退到原始方法
        return getWPSImageRIdDirect(zipFile, imageId);
    }

    /**
     * 获取或构建WPS图片映射缓存（线程安全版本）
     * 使用 computeIfAbsent 确保在多线程环境下的原子性操作
     */
    private Map<String, String> getWPSImageMappingCached(ZipFile zipFile, String fileName) {
        // 使用 computeIfAbsent 确保线程安全的缓存操作
        // 如果缓存中不存在，则构建映射；如果存在，直接返回
        return wpsImageMappingCache.computeIfAbsent(fileName, key -> {
            Map<String, String> mapping = buildWPSImageMapping(zipFile);
            if (mapping != null) {
                log.debug("Cached WPS image mapping for {} with {} entries (total cache size: {})",
                         fileName, mapping.size(), wpsImageMappingCache.size());
                return mapping;
            }
            // 如果构建失败，返回空Map而不是null，避免重复尝试
            return new ConcurrentHashMap<>();
        });
    }

    /**
     * 构建完整的WPS图片ID到rId的映射
     * 性能优化：一次性构建所有映射，将O(n)查找优化为O(1)
     */
    private Map<String, String> buildWPSImageMapping(ZipFile zipFile) {
        ZipEntry entry = zipFile.getEntry("xl/cellimages.xml");
        if (entry == null) {
            log.debug("xl/cellimages.xml not found - not a WPS DISPIMG file");
            return null;
        }

        Map<String, String> mapping = new ConcurrentHashMap<>();

        try (InputStream is = zipFile.getInputStream(entry)) {
            Document doc = createNamespaceAwareDocument(is);
            NodeList cellImages = doc.getElementsByTagNameNS(WPS_NAMESPACE, "cellImage");

            log.debug("Building WPS image mapping from {} etc:cellImage elements", cellImages.getLength());

            for (int i = 0; i < cellImages.getLength(); i++) {
                Element cellImage = (Element) cellImages.item(i);

                // 使用辅助方法获取name和embed属性
                String imageName = getElementAttribute(cellImage, XDR_NAMESPACE, "cNvPr", "name");
                String embedId = getElementAttributeNS(cellImage, A_NAMESPACE, "blip", R_NAMESPACE, "embed");

                if (imageName != null && embedId != null) {
                    mapping.put(imageName, embedId);
                    log.debug("WPS DISPIMG mapping added: {} -> {}", imageName, embedId);
                }
            }

            return mapping;

        } catch (Exception e) {
            log.error("Error building WPS image mapping", e);
            return null;
        }
    }

    /**
     * 直接从XML获取WPS图片rId（不使用缓存）
     * 作为缓存失败时的回退方案
     */
    private String getWPSImageRIdDirect(ZipFile zipFile, String imageId) {
        ZipEntry entry = zipFile.getEntry("xl/cellimages.xml");
        if (entry == null) {
            log.debug("xl/cellimages.xml not found - not a WPS DISPIMG file");
            return null;
        }

        try (InputStream is = zipFile.getInputStream(entry)) {
            Document doc = createNamespaceAwareDocument(is);
            NodeList cellImages = doc.getElementsByTagNameNS(WPS_NAMESPACE, "cellImage");

            log.debug("Found {} etc:cellImage elements (direct lookup)", cellImages.getLength());

            for (int i = 0; i < cellImages.getLength(); i++) {
                Element cellImage = (Element) cellImages.item(i);

                // 使用辅助方法获取name和embed属性
                String imageName = getElementAttribute(cellImage, XDR_NAMESPACE, "cNvPr", "name");
                String embedId = getElementAttributeNS(cellImage, A_NAMESPACE, "blip", R_NAMESPACE, "embed");

                if (imageId.equals(imageName) && embedId != null) {
                    log.debug("WPS DISPIMG mapping (direct): {} -> {}", imageName, embedId);
                    return embedId;
                }
            }

            log.debug("No matching DISPIMG ID found (direct): {}", imageId);

        } catch (Exception e) {
            log.error("Error parsing WPS cellimages.xml (direct)", e);
        }

        return null;
    }

    /**
     * 从xl/_rels/cellimages.xml.rels获取rId到图片路径的映射
     * 解析XML格式：<Relationship Id="rId1" Target="media/image1.png"/>
     */
    private String getWPSImagePathFromRId(ZipFile zipFile, String rId) {
        ZipEntry relsEntry = zipFile.getEntry("xl/_rels/cellimages.xml.rels");
        if (relsEntry == null) {
            log.debug("xl/_rels/cellimages.xml.rels not found");
            return null;
        }

        try (InputStream is = zipFile.getInputStream(relsEntry)) {
            XMLStreamReader reader = xmlInputFactory.createXMLStreamReader(is);

            while (reader.hasNext()) {
                int event = reader.next();
                if (event == XMLStreamConstants.START_ELEMENT) {
                    String elementName = reader.getLocalName();

                    if ("Relationship".equals(elementName)) {
                        String id = reader.getAttributeValue(null, "Id");
                        String target = reader.getAttributeValue(null, "Target");

                        if (rId.equals(id) && target != null) {
                            log.debug("Found rId mapping: {} -> {}", rId, target);
                            return target;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error parsing xl/_rels/cellimages.xml.rels", e);
        }

        return null;
    }

    /**
     * 创建支持命名空间的DOM文档
     */
    private Document createNamespaceAwareDocument(InputStream is) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        return factory.newDocumentBuilder().parse(is);
    }

    /**
     * 获取指定命名空间元素的属性值
     */
    private String getElementAttribute(Element parent, String namespace, String elementName, String attrName) {
        NodeList nodes = parent.getElementsByTagNameNS(namespace, elementName);
        if (nodes.getLength() > 0) {
            String value = ((Element) nodes.item(0)).getAttribute(attrName);
            return value != null && !value.trim().isEmpty() ? value.trim() : null;
        }
        return null;
    }

    /**
     * 获取指定命名空间元素的命名空间属性值
     */
    private String getElementAttributeNS(Element parent, String elementNamespace, String elementName,
                                         String attrNamespace, String attrName) {
        NodeList nodes = parent.getElementsByTagNameNS(elementNamespace, elementName);
        if (nodes.getLength() > 0) {
            String value = ((Element) nodes.item(0)).getAttributeNS(attrNamespace, attrName);
            return value != null && !value.trim().isEmpty() ? value.trim() : null;
        }
        return null;
    }

    /**
     * 清理指定文件的WPS图片映射缓存
     * 当文件被修改或需要强制刷新特定文件的缓存时调用
     *
     * @param fileName 要清理缓存的文件名
     * @return 是否成功清理了缓存（true表示缓存存在并被清理，false表示缓存不存在）
     */
    public boolean clearWpsImageMappingCache(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            log.warn("Cannot clear cache for null or empty fileName");
            return false;
        }

        Map<String, String> removedMapping = wpsImageMappingCache.remove(fileName);
        if (removedMapping != null) {
            log.info("Cleared WPS image mapping cache for file: {} (removed {} mappings)",
                    fileName, removedMapping.size());
            return true;
        } else {
            log.debug("No cache found for file: {}", fileName);
            return false;
        }
    }
}
